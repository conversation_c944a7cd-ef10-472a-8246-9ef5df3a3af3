/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #ffffff;
    overflow-x: hidden;
    min-height: 100vh;
}

/* 容器和背景 */
.container {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px 20px;
}

.background-layer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/background.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    z-index: -2;
}

.background-layer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.5));
    z-index: -1;
}

/* 主内容区域 */
.main-content {
    max-width: 450px;
    width: 100%;
    text-align: center;
    z-index: 1;
    animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.logo-image {
    max-width: 280px;
    width: 100%;
    height: auto;
    border-radius: 20px;
    transition: transform 0.3s ease;
}

.logo-image:hover {
    transform: scale(1.02);
}

/* 游戏信息 */
.game-info {
    margin-bottom: 40px;
}

.game-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    background: linear-gradient(135deg, #4facfe, #00f2fe);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 10px rgba(79, 172, 254, 0.3);
}

.game-description {
    font-size: 1rem;
    color: #e0e0e0;
    line-height: 1.6;
    margin-bottom: 20px;
}

/* 社交媒体按钮 */
.social-buttons {
    margin-bottom: 35px;
}

/* Discord按钮 - 占一行 */
.discord-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 12px;
    border-radius: 15px;
    text-decoration: none;
    color: white;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 60px;
    white-space: nowrap;
    width: 100%;
    margin-bottom: 12px;
    background: linear-gradient(135deg, #5865f2, #4752c4);
    box-shadow: 0 4px 15px rgba(88, 101, 242, 0.3);
}

.discord-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.discord-btn:hover::before {
    left: 100%;
}

.discord-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(88, 101, 242, 0.4);
}

/* 其他按钮的网格容器 */
.social-buttons-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

/* 网格中的按钮样式 */
.social-buttons-grid .social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px 12px;
    border-radius: 15px;
    text-decoration: none;
    color: white;
    font-weight: 500;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 54px;
    white-space: nowrap;
}

.social-buttons-grid .social-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.social-buttons-grid .social-btn:hover::before {
    left: 100%;
}

.btn-icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.btn-icon svg {
    width: 100%;
    height: 100%;
}

/* 各平台按钮样式 */
.discord-btn {
    background: linear-gradient(135deg, #5865f2, #4752c4);
    box-shadow: 0 4px 15px rgba(88, 101, 242, 0.3);
}

.discord-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(88, 101, 242, 0.4);
}

.facebook-btn {
    background: linear-gradient(135deg, #1877f2, #166fe5);
    box-shadow: 0 4px 15px rgba(24, 119, 242, 0.3);
}

.facebook-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(24, 119, 242, 0.4);
}

.twitter-btn {
    background: linear-gradient(135deg, #000000, #333333);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.twitter-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.instagram-btn {
    background: linear-gradient(135deg, #e4405f, #c13584, #833ab4);
    box-shadow: 0 4px 15px rgba(228, 64, 95, 0.3);
}

.instagram-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(228, 64, 95, 0.4);
}

.youtube-btn {
    background: linear-gradient(135deg, #ff0000, #cc0000);
    box-shadow: 0 4px 15px rgba(255, 0, 0, 0.3);
}

.youtube-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 0, 0, 0.4);
}

/* 底部信息 */
.footer-info {
    color: #a0a0a0;
    font-size: 0.8rem;
}

/* 响应式设计 */

/* 桌面端大屏幕 */
@media (min-width: 1024px) {
    .main-content {
        max-width: 500px;
    }

    .logo-image {
        max-width: 320px;
    }

    .game-logo {
        margin-bottom: 40px;
    }

    .game-info {
        margin-bottom: 45px;
    }
}

/* 平板端 */
@media (min-width: 768px) and (max-width: 1023px) {
    .main-content {
        max-width: 480px;
    }

    .logo-image {
        max-width: 300px;
    }
}

/* 长屏手机优化 (高宽比 > 2:1) */
@media (max-width: 480px) and (min-height: 800px) {
    .container {
        padding: 50px 20px;
        align-items: flex-start;
        padding-top: 15vh;
    }

    .main-content {
        max-width: 380px;
    }

    .logo-image {
        max-width: 220px;
    }

    .game-logo {
        margin-bottom: 40px;
    }

    .game-info {
        margin-bottom: 45px;
    }

    .social-buttons {
        margin-bottom: 50px;
    }
}

/* 普通手机端 */
@media (max-width: 480px) {
    .main-content {
        max-width: 350px;
        padding: 0 10px;
    }

    .game-title {
        font-size: 2rem;
    }

    .social-btn {
        padding: 16px 10px;
        font-size: 0.8rem;
        min-height: 52px;
    }

    /* Discord按钮在手机端保持更大的高度 */
    .discord-btn {
        padding: 20px 12px;
        min-height: 60px;
        font-size: 0.9rem;
    }

    .btn-icon {
        width: 18px;
        height: 18px;
        margin-right: 6px;
    }

    .logo-image {
        max-width: 280px;
    }
}

/* 小屏手机端 */
@media (max-width: 360px) {
    .container {
        padding: 20px 15px;
    }

    .main-content {
        max-width: 320px;
    }

    .game-title {
        font-size: 1.8rem;
    }

    .social-btn {
        padding: 14px 8px;
        font-size: 0.75rem;
        min-height: 50px;
    }

    /* Discord按钮在小屏手机端也保持更大的高度 */
    .discord-btn {
        padding: 18px 12px;
        min-height: 58px;
        font-size: 0.85rem;
    }

    .btn-icon {
        width: 16px;
        height: 16px;
        margin-right: 5px;
    }

    .social-buttons {
        gap: 12px;
    }

    .logo-image {
        max-width: 160px;
    }
}

/* 超长屏手机优化 (高宽比 > 2.5:1) */
@media (max-width: 480px) and (min-height: 1000px) {
    .container {
        padding-top: 12vh;
        padding-bottom: 8vh;
    }

    .game-logo {
        margin-bottom: 50px;
    }

    .game-info {
        margin-bottom: 50px;
    }

    .social-buttons {
        margin-bottom: 60px;
    }

    .logo-image {
        max-width: 240px;
    }
}

.social-btn:active {
    transform: scale(0.98);
}
