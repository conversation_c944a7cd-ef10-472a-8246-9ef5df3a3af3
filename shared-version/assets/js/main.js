// Meta Pixel 事件追踪
(function() {
    'use strict';

    // 社交媒体平台配置
    const socialPlatforms = {
        'Discord': 'SocialClick_Discord',
        'Facebook': 'SocialClick_Facebook',
        'Twitter': 'SocialClick_Twitter',
        'Instagram': 'SocialClick_Instagram',
        'YouTube': 'SocialClick_YouTube'
    };

    // Meta Pixel 事件追踪函数
    function trackPixelEvent(eventName, platform, url) {
        try {
            if (typeof fbq !== 'undefined') {
                fbq('trackCustom', eventName, {
                    platform: platform,
                    destination_url: url
                });
            }
        } catch (error) {
            console.error('Meta Pixel追踪失败:', error);
        }
    }

    // 社交媒体按钮点击处理
    function handleSocialClick(event) {
        const button = event.currentTarget;
        const platform = button.getAttribute('data-platform');
        const url = button.href;

        if (!platform || !url) return;

        const eventName = socialPlatforms[platform];
        if (!eventName) return;

        // 追踪 Meta Pixel 事件
        trackPixelEvent(eventName, platform, url);

        // 延迟跳转以确保事件被追踪
        event.preventDefault();
        setTimeout(() => {
            window.open(url, '_blank', 'noopener,noreferrer');
        }, 100);
    }

    // 页面初始化
    function initializePage() {
        // 绑定社交媒体按钮事件
        const socialButtons = document.querySelectorAll('.social-btn');
        socialButtons.forEach(button => {
            button.addEventListener('click', handleSocialClick);
        });

        // 追踪页面加载事件
        if (typeof fbq !== 'undefined') {
            fbq('track', 'ViewContent', {
                content_name: 'Keep Fishing Landing Page',
                content_category: 'Game Download'
            });
        }
    }

    // DOM 加载完成后执行初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializePage);
    } else {
        initializePage();
    }

})();
